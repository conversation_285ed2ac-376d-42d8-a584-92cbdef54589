
import React from 'react';

interface KpiCardProps {
    title: string;
    value: string | number;
    children?: React.ReactNode;
}

const KpiCard: React.FC<KpiCardProps> = ({ title, value, children }) => {
    return (
        <div className="bg-white dark:bg-gray-800 p-5 rounded-xl shadow-md transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <h3 className="text-base font-semibold text-gray-500 dark:text-gray-400 truncate">{title}</h3>
            <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">{value}</p>
            <div className="text-gray-600 dark:text-gray-300">{children}</div>
        </div>
    );
};

export default KpiCard;
