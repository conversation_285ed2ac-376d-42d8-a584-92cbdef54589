
import React, { useState, useCallback } from 'react';
import { Kpi, OpdData } from '../types';
import { generateDashboardInsights } from '../services/geminiService';

interface AiInsightCardProps {
    kpis: Kpi;
    filteredData: OpdData[];
}

const SparkleIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M12 2L9 9l-7 3 7 3 3 7 3-7 7-3-7-3z" />
        <path d="M22 12l-3-1-1-3-1 3-3 1 3 1 1 3 1-3 3-1z" />
    </svg>
);

const AiInsightCard: React.FC<AiInsightCardProps> = ({ kpis, filteredData }) => {
    const [insights, setInsights] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string>('');

    const handleGenerateInsights = useCallback(async () => {
        setIsLoading(true);
        setError('');
        setInsights('');
        try {
            const result = await generateDashboardInsights(kpis, filteredData);
            setInsights(result);
        } catch (e: any) {
            setError('Failed to generate insights.');
            console.error(e);
        } finally {
            setIsLoading(false);
        }
    }, [kpis, filteredData]);
    
    // Format insights for better display
    const formattedInsights = insights.split('\n').map((line, index) => {
        if (line.trim().startsWith('* ') || line.trim().startsWith('- ')) {
            return <li key={index} className="mb-2 text-gray-600 dark:text-gray-300">{line.substring(2)}</li>;
        }
        return null;
    }).filter(Boolean);

    return (
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-gray-800 dark:to-gray-800 p-5 rounded-xl shadow-md border border-indigo-200 dark:border-gray-700">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                        <SparkleIcon />
                        AI-Powered Insights
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mt-1">Click the button to analyze the current view and get actionable insights.</p>
                </div>
                <button 
                    onClick={handleGenerateInsights}
                    disabled={isLoading}
                    className="mt-3 md:mt-0 px-4 py-2 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 disabled:bg-indigo-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-all duration-200 flex items-center gap-2"
                >
                    {isLoading ? 'Analyzing...' : 'Generate Insights'}
                </button>
            </div>
            
            <div className="mt-4">
                {isLoading && (
                    <div className="flex items-center justify-center h-24">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    </div>
                )}
                {error && <p className="text-red-500">{error}</p>}
                {insights && !error && (
                    <div className="bg-white dark:bg-gray-700/50 p-4 rounded-lg">
                        <ul className="list-none pl-0">
                            {formattedInsights}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AiInsightCard;
