
import React from 'react';

interface FilterBarProps {
    options: {
        unit: string[];
        speciality: string[];
        doctor: string[];
    };
    filters: {
        unit: string;
        speciality: string;
        doctor: string;
    };
    onChange: (filterType: string, value: string) => void;
    onReset: () => void;
}

const FilterSelect: React.FC<{label: string, value: string, options: string[], onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void}> = ({ label, value, options, onChange }) => (
    <div className="flex flex-col">
        <label htmlFor={label} className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{label}</label>
        <select 
            id={label}
            value={value}
            onChange={onChange}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
        >
            <option value="All">All</option>
            {options.map(opt => <option key={opt} value={opt}>{opt}</option>)}
        </select>
    </div>
);

const FilterBar: React.FC<FilterBarProps> = ({ options, filters, onChange, onReset }) => {
    return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                <FilterSelect label="Unit" value={filters.unit} options={options.unit} onChange={(e) => onChange('unit', e.target.value)} />
                <FilterSelect label="Doctor Speciality" value={filters.speciality} options={options.speciality} onChange={(e) => onChange('speciality', e.target.value)} />
                <FilterSelect label="Performing Doctor" value={filters.doctor} options={options.doctor} onChange={(e) => onChange('doctor', e.target.value)} />
                <button onClick={onReset} className="w-full md:w-auto bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 font-semibold py-2 px-4 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors">
                    Reset Filters
                </button>
            </div>
        </div>
    );
};

export default FilterBar;
