
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';

interface HourlyTrendChartProps {
    data: { name: string; Transactions: number }[];
}

const HourlyTrendChart: React.FC<HourlyTrendChartProps> = ({ data }) => {
    return (
        <>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Hourly Service Trend</h3>
            <div style={{ width: '100%', height: 320 }}>
                <ResponsiveContainer>
                    <AreaChart data={data} margin={{ top: 5, right: 20, left: -15, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(128, 128, 128, 0.2)" />
                        <XAxis dataKey="name" tick={{ fill: 'var(--recharts-text-color, #6b7280)' }} fontSize={12} />
                        <YAxis tick={{ fill: 'var(--recharts-text-color, #6b7280)' }} fontSize={12}/>
                        <Tooltip
                            cursor={{ fill: 'rgba(239, 246, 255, 0.5)' }}
                            contentStyle={{ 
                                backgroundColor: 'rgba(255, 255, 255, 0.8)', 
                                backdropFilter: 'blur(5px)',
                                border: '1px solid #e5e7eb',
                                borderRadius: '0.5rem',
                                color: '#1f2937'
                            }}
                        />
                         <defs>
                            <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                                <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                            </linearGradient>
                        </defs>
                        <Area type="monotone" dataKey="Transactions" stroke="#10b981" fillOpacity={1} fill="url(#colorUv)" strokeWidth={2} />
                    </AreaChart>
                </ResponsiveContainer>
            </div>
        </>
    );
};

export default HourlyTrendChart;
