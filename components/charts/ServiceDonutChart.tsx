
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface ServiceDonutChartProps {
    data: { name: string; value: number }[];
    title: string;
}

const COLORS = ['#3b82f6', '#10b981', '#f97316', '#ef4444', '#8b5cf6', '#d946ef'];

const ServiceDonutChart: React.FC<ServiceDonutChartProps> = ({ data, title }) => {
    return (
        <>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">{title}</h3>
            <div style={{ width: '100%', height: 250 }}>
                <ResponsiveContainer>
                    <PieChart>
                        <Pie
                            data={data}
                            cx="50%"
                            cy="50%"
                            innerRadius={60}
                            outerRadius={80}
                            fill="#8884d8"
                            paddingAngle={5}
                            dataKey="value"
                            nameKey="name"
                        >
                            {data.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                        </Pie>
                        <Tooltip
                            contentStyle={{ 
                                backgroundColor: 'rgba(255, 255, 255, 0.8)', 
                                backdropFilter: 'blur(5px)',
                                border: '1px solid #e5e7eb',
                                borderRadius: '0.5rem',
                                color: '#1f2937'
                            }}
                        />
                        <Legend iconSize={10} layout="vertical" verticalAlign="middle" align="right" 
                            wrapperStyle={{fontSize: "12px", lineHeight: "1.5"}}
                        />
                    </PieChart>
                </ResponsiveContainer>
            </div>
        </>
    );
};

export default ServiceDonutChart;
