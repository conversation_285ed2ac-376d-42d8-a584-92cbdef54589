
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Cartesian<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';

interface AgeGenderChartProps {
    data: { name: string; Male: number; Female: number }[];
}

const AgeGenderChart: React.FC<AgeGenderChartProps> = ({ data }) => {
    return (
        <>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Age & Gender Distribution</h3>
            <div style={{ width: '100%', height: 320 }}>
                <ResponsiveContainer>
                    <BarChart data={data} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(128, 128, 128, 0.2)" />
                        <XAxis dataKey="name" tick={{ fill: 'var(--recharts-text-color, #6b7280)' }} fontSize={12} />
                        <YAxis tick={{ fill: 'var(--recharts-text-color, #6b7280)' }} fontSize={12}/>
                        <Tooltip
                            cursor={{ fill: 'rgba(239, 246, 255, 0.5)' }}
                            contentStyle={{ 
                                backgroundColor: 'rgba(255, 255, 255, 0.8)', 
                                backdropFilter: 'blur(5px)',
                                border: '1px solid #e5e7eb',
                                borderRadius: '0.5rem',
                                color: '#1f2937'
                            }}
                        />
                        <Legend wrapperStyle={{fontSize: "14px"}} />
                        <Bar dataKey="Male" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="Female" fill="#ec4899" radius={[4, 4, 0, 0]} />
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </>
    );
};

export default AgeGenderChart;
