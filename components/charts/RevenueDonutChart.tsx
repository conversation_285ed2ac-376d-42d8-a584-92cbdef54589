
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface RevenueDonutChartProps {
    data: { name: string; value: number }[];
    title: string;
}

const COLORS = ['#22c55e', '#f59e0b', '#3b82f6', '#a855f7', '#64748b'];
const currencyFormatter = new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', minimumFractionDigits: 0 });

const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
        return (
            <div className="p-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border dark:border-gray-600 rounded-lg shadow-lg">
                <p className="label text-gray-800 dark:text-gray-100">{`${payload[0].name} : ${currencyFormatter.format(payload[0].value)}`}</p>
            </div>
        );
    }
    return null;
};

const RevenueDonutChart: React.FC<RevenueDonutChartProps> = ({ data, title }) => {
    return (
        <>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">{title}</h3>
            <div style={{ width: '100%', height: 250 }}>
                <ResponsiveContainer>
                    <PieChart>
                        <Pie
                            data={data}
                            cx="50%"
                            cy="50%"
                            innerRadius={60}
                            outerRadius={80}
                            fill="#8884d8"
                            paddingAngle={5}
                            dataKey="value"
                            nameKey="name"
                        >
                            {data.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                        </Pie>
                        <Tooltip content={<CustomTooltip />} />
                        <Legend iconSize={10} layout="vertical" verticalAlign="middle" align="right" 
                            wrapperStyle={{fontSize: "12px", lineHeight: "1.5"}}
                        />
                    </PieChart>
                </ResponsiveContainer>
            </div>
        </>
    );
};

export default RevenueDonutChart;
