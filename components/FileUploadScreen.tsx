
import React, { useCallback } from 'react';
import <PERSON> from 'papaparse';
import { useDropzone } from 'react-dropzone';
import { OpdData } from '../types';

interface FileUploadScreenProps {
    onFileUpload: (data: OpdData[], file: File) => void;
    isLoading: boolean;
    setIsLoading: (loading: boolean) => void;
    error: string | null;
    setError: (error: string | null) => void;
}

const UploadIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto h-12 w-12 text-gray-400">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
        <polyline points="17 8 12 3 7 8"></polyline>
        <line x1="12" y1="3" x2="12" y2="15"></line>
    </svg>
);


const FileUploadScreen: React.FC<FileUploadScreenProps> = ({ onFileUpload, isLoading, setIsLoading, error, setError }) => {
    
    const onDrop = useCallback(<T extends File,>(acceptedFiles: T[]) => {
        setError(null);
        if (acceptedFiles.length > 0) {
            const file = acceptedFiles[0];
            if (!file.name.endsWith('.csv')) {
                setError('Invalid file type. Please upload a .csv file.');
                return;
            }
            setIsLoading(true);
            Papa.parse(file, {
                header: true,
                skipEmptyLines: true,
                complete: (results) => {
                    onFileUpload(results.data as OpdData[], file);
                },
                error: (err: any) => {
                    setError(`Error parsing CSV file: ${err.message}`);
                    setIsLoading(false);
                }
            });
        }
    }, [onFileUpload, setIsLoading, setError]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop, accept: {'text/csv': ['.csv']} });

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
            <div className="w-full max-w-2xl">
                <h1 className="text-4xl font-bold text-gray-800 dark:text-white">AI-Powered OPD Dashboard</h1>
                <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
                    Gain instant insights from your Outpatient Department data.
                </p>

                <div 
                    {...getRootProps()} 
                    className={`mt-8 p-10 border-2 border-dashed rounded-xl cursor-pointer transition-colors duration-200
                        ${isDragActive ? 'border-indigo-600 bg-indigo-50 dark:bg-indigo-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-indigo-500 dark:hover:border-indigo-400'}
                    `}
                >
                    <input {...getInputProps()} />
                    <UploadIcon />
                    <h2 className="mt-4 text-xl font-semibold text-gray-900 dark:text-white">
                        {isDragActive ? 'Drop the file here...' : 'Drag & drop your CSV file here'}
                    </h2>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">or click to select a file</p>
                </div>
                
                {isLoading && <p className="mt-4 text-indigo-600 dark:text-indigo-400">Processing your file...</p>}
                {error && <p className="mt-4 text-red-600 dark:text-red-400">{error}</p>}

                <div className="mt-10 text-left text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800/50 p-6 rounded-lg">
                    <h3 className="font-semibold text-gray-800 dark:text-white mb-2">CSV File Requirements:</h3>
                    <ul className="list-disc list-inside text-sm space-y-1">
                        <li>Must be a valid CSV file.</li>
                        <li>Headers are required and should match expected fields (e.g., `unit`, `uhid`, `gender`, `age`, `netamount`).</li>
                        <li>Dates (like `billdate`) should be in a consistent, machine-readable format (e.g., YYYY-MM-DD HH:MM:SS).</li>
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default FileUploadScreen;
