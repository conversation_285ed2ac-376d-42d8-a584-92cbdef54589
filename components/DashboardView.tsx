
import React, { useState, useMemo, useCallback } from 'react';
import { OpdData, Kpi } from '../types';
import { calculateKpis, getUniqueValues, processAgeGenderData, processHourlyData, processServiceDonutData, processRevenueDonutData } from '../utils/dataProcessor';
import FilterBar from './FilterBar';
import KpiCard from './KpiCard';
import AgeGenderChart from './charts/AgeGenderChart';
import HourlyTrendChart from './charts/HourlyTrendChart';
import ServiceDonutChart from './charts/ServiceDonutChart';
import RevenueDonutChart from './charts/RevenueDonutChart';
import AiInsightCard from './AiInsightCard';

interface DashboardViewProps {
    data: OpdData[];
    fileName: string;
    onReset: () => void;
}

const Header: React.FC<{fileName: string, onReset: () => void}> = ({fileName, onReset}) => (
     <header className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">OPD Dashboard</h1>
            <p className="text-gray-500 dark:text-gray-400">
                Displaying data from <span className="font-semibold text-indigo-600 dark:text-indigo-400">{fileName}</span>
            </p>
        </div>
        <button 
            onClick={onReset} 
            className="mt-2 md:mt-0 bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-900 transition-all duration-200 flex items-center gap-2"
        >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M4 4v5h5"/><path d="M10 4.2A9 9 0 0 1 21.5 12a9 9 0 0 1-13.3 6.9"/><path d="M20 20v-5h-5"/><path d="M4 19.8A9 9 0 0 1 2.5 12a9 9 0 0 1 13.3-6.9"/></svg>
            Upload New File
        </button>
    </header>
);

const DashboardView: React.FC<DashboardViewProps> = ({ data, fileName, onReset }) => {
    const [filters, setFilters] = useState({
        unit: 'All',
        speciality: 'All',
        doctor: 'All',
    });

    const filterOptions = useMemo(() => ({
        unit: getUniqueValues(data, 'unit'),
        speciality: getUniqueValues(data, 'performingdoctorspeciality'),
        doctor: getUniqueValues(data, 'performingdoctor'),
    }), [data]);

    const filteredData = useMemo(() => {
        return data.filter(row => {
            const unitMatch = filters.unit === 'All' || row.unit?.trim() === filters.unit;
            const specialityMatch = filters.speciality === 'All' || row.performingdoctorspeciality?.trim() === filters.speciality;
            const doctorMatch = filters.doctor === 'All' || row.performingdoctor?.trim() === filters.doctor;
            return unitMatch && specialityMatch && doctorMatch;
        });
    }, [data, filters]);

    const kpis: Kpi = useMemo(() => calculateKpis(filteredData, data), [filteredData, data]);
    const ageGenderData = useMemo(() => processAgeGenderData(filteredData), [filteredData]);
    const hourlyData = useMemo(() => processHourlyData(filteredData), [filteredData]);
    const labServicesData = useMemo(() => processServiceDonutData(filteredData, 'lab', 'subspecialization'), [filteredData]);
    const radiologyServicesData = useMemo(() => processServiceDonutData(filteredData, 'radiology', 'subspecialization'), [filteredData]);
    const revenueByPayerData = useMemo(() => processRevenueDonutData(filteredData), [filteredData]);
    
    const handleFilterChange = useCallback((filterType: string, value: string) => {
        setFilters(prev => ({...prev, [filterType]: value}));
    }, []);

    const handleResetFilters = useCallback(() => {
        setFilters({ unit: 'All', speciality: 'All', doctor: 'All' });
    }, []);

    const currencyFormatter = new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', minimumFractionDigits: 0 });

    return (
        <div className="p-4 md:p-6 lg:p-8">
            <Header fileName={fileName} onReset={onReset} />
            <FilterBar options={filterOptions} filters={filters} onChange={handleFilterChange} onReset={handleResetFilters} />
            
            <main className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mt-6">
                {/* KPIs */}
                <KpiCard title="Total Transactions" value={kpis.totalTransactions.toLocaleString('en-IN')}>
                    <div className="text-sm space-y-1.5 mt-3">
                        <div className="flex justify-between"><span>OPD Visits</span><span className="font-semibold">{kpis.opdVisits.toLocaleString('en-IN')} ({kpis.totalTransactions > 0 ? (kpis.opdVisits / kpis.totalTransactions * 100).toFixed(0) : 0}%)</span></div>
                        <div className="flex justify-between"><span>OPD Services</span><span className="font-semibold">{kpis.opdServices.toLocaleString('en-IN')} ({kpis.totalTransactions > 0 ? (kpis.opdServices / kpis.totalTransactions * 100).toFixed(0) : 0}%)</span></div>
                    </div>
                </KpiCard>
                <KpiCard title="Total Unique Patients" value={kpis.totalUniquePatients.toLocaleString('en-IN')}>
                    <div className="text-sm space-y-1.5 mt-3">
                        <div className="flex justify-between"><span>New Visit</span><span className="font-semibold">{kpis.newVisits.toLocaleString('en-IN')} ({kpis.totalUniquePatients > 0 ? (kpis.newVisits / kpis.totalUniquePatients * 100).toFixed(0) : 0}%)</span></div>
                        <div className="flex justify-between"><span>Emergency</span><span className="font-semibold">{kpis.emergencyVisits.toLocaleString('en-IN')} ({kpis.totalUniquePatients > 0 ? (kpis.emergencyVisits / kpis.totalUniquePatients * 100).toFixed(0) : 0}%)</span></div>
                        <div className="flex justify-between"><span>Others</span><span className="font-semibold">{kpis.otherVisits.toLocaleString('en-IN')} ({kpis.totalUniquePatients > 0 ? (kpis.otherVisits / kpis.totalUniquePatients * 100).toFixed(0) : 0}%)</span></div>
                    </div>
                </KpiCard>
                <KpiCard title="New Patient Registrations" value={kpis.patientsRegisteredToday.toLocaleString('en-IN')}>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">Patients registered on {kpis.latestRegDate}.</p>
                </KpiCard>
                <KpiCard title="Total Revenue" value={currencyFormatter.format(kpis.totalRevenue)}>
                     <p className="text-sm mt-3">
                        Total Concessions: <span className="font-semibold text-red-500">{currencyFormatter.format(kpis.totalConcessions)}</span>
                    </p>
                </KpiCard>

                {/* Charts */}
                <div className="md:col-span-2 xl:col-span-4"><AiInsightCard kpis={kpis} filteredData={filteredData} /></div>
                
                <div className="md:col-span-2 xl:col-span-3 bg-white dark:bg-gray-800 p-4 rounded-xl shadow-md"><AgeGenderChart data={ageGenderData} /></div>
                <div className="md:col-span-2 xl:col-span-1 bg-white dark:bg-gray-800 p-4 rounded-xl shadow-md"><HourlyTrendChart data={hourlyData} /></div>
                
                <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-md"><ServiceDonutChart data={labServicesData} title="LAB Services"/></div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-md"><ServiceDonutChart data={radiologyServicesData} title="RADIOLOGY Services"/></div>
                <div className="md:col-span-2 xl:col-span-2 bg-white dark:bg-gray-800 p-4 rounded-xl shadow-md"><RevenueDonutChart data={revenueByPayerData} title="Revenue by Payer Type"/></div>
            </main>
        </div>
    );
};

export default DashboardView;
