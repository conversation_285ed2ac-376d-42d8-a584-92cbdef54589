
import React, { useState } from 'react';
import { OpdData } from './types';
import FileUploadScreen from './components/FileUploadScreen';
import DashboardView from './components/DashboardView';

const App: React.FC = () => {
    const [data, setData] = useState<OpdData[]>([]);
    const [fileName, setFileName] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const handleFileUpload = (parsedData: OpdData[], file: File) => {
        if (parsedData.length > 0) {
            setData(parsedData);
            setFileName(file.name);
            setError(null);
        } else {
            setError('The uploaded CSV file is empty or could not be parsed correctly.');
            setData([]);
            setFileName('');
        }
        setIsLoading(false);
    };

    const handleReset = () => {
        setData([]);
        setFileName('');
        setError(null);
    };

    return (
        <div className="min-h-screen bg-gray-50 text-gray-800 dark:bg-gray-900 dark:text-gray-200 transition-colors duration-300">
            {data.length === 0 ? (
                <FileUploadScreen 
                    onFileUpload={handleFileUpload} 
                    isLoading={isLoading} 
                    setIsLoading={setIsLoading}
                    error={error}
                    setError={setError}
                />
            ) : (
                <DashboardView data={data} fileName={fileName} onReset={handleReset} />
            )}
        </div>
    );
};

export default App;
