
import { OpdData, Kpi } from '../types';

export const getUniqueValues = (data: OpdData[], key: keyof OpdData): string[] => {
    const values = data.map(item => {
        const value = item[key];
        if (value) {
            return String(value).trim();
        }
        return null;
    });
    return [...new Set(values.filter(Boolean) as string[])].sort();
};

export const calculateKpis = (filteredData: OpdData[], allData: OpdData[]): Kpi => {
    const billedData = filteredData.filter(d => String(d.trantype || '').toLowerCase().trim() === 'billed');
    const totalTransactions = billedData.length;

    const consultationTypes = ['consultation', 'consultation.'];
    const opdVisits = billedData.filter(d => consultationTypes.includes(String(d.servicetype || '').toLowerCase().trim())).length;
    const opdServices = totalTransactions - opdVisits;

    const uniquePatients = new Set(billedData.map(d => d.uhid));
    const totalUniquePatients = uniquePatients.size;

    const getUniquePatientCountByVisitType = (visitType: string) => {
        return new Set(billedData.filter(d => String(d.visittype || '').toLowerCase().trim() === visitType).map(d => d.uhid)).size;
    };

    const newVisits = getUniquePatientCountByVisitType('new visit');
    const emergencyVisits = getUniquePatientCountByVisitType('emergency');
    const otherVisits = totalUniquePatients - newVisits - emergencyVisits;

    const registrationDates = allData.map(d => d.registrationdate).filter(Boolean).map(d => new Date(d as string));
    const latestDate = registrationDates.length ? new Date(Math.max.apply(null, registrationDates.map(d => d.getTime()))) : new Date();
    const latestDateString = latestDate.toISOString().split('T')[0];
    
    const patientsRegisteredToday = new Set(allData.filter(d => d.registrationdate === latestDateString).map(d => d.uhid)).size;
    
    const totalRevenue = billedData.reduce((sum, row) => sum + (parseFloat(String(row.netamount || '0'))), 0);
    const totalConcessions = billedData.reduce((sum, row) => sum + (parseFloat(String(row.concessionamount || '0'))), 0);

    return {
        totalTransactions,
        opdVisits,
        opdServices,
        totalUniquePatients,
        newVisits,
        emergencyVisits,
        otherVisits,
        patientsRegisteredToday,
        latestRegDate: latestDate.toLocaleDateString('en-GB'),
        totalRevenue,
        totalConcessions
    };
};

export const processAgeGenderData = (data: OpdData[]) => {
    const uniquePatients = [...new Map(data.map(item => [item.uhid, item])).values()];
    const ageBuckets: { [key: string]: { Male: number; Female: number } } = {
        '0-10': { Male: 0, Female: 0 }, '11-20': { Male: 0, Female: 0 }, '21-30': { Male: 0, Female: 0 },
        '31-40': { Male: 0, Female: 0 }, '41-50': { Male: 0, Female: 0 }, '51-60': { Male: 0, Female: 0 },
        '61-70': { Male: 0, Female: 0 }, '71-80': { Male: 0, Female: 0 }, '80+': { Male: 0, Female: 0 },
    };

    uniquePatients.forEach(p => {
        const age = parseInt(String(p.age || ''), 10);
        if (isNaN(age)) return;
        
        let bucket = '80+';
        if (age <= 10) bucket = '0-10';
        else if (age <= 20) bucket = '11-20';
        else if (age <= 30) bucket = '21-30';
        else if (age <= 40) bucket = '31-40';
        else if (age <= 50) bucket = '41-50';
        else if (age <= 60) bucket = '51-60';
        else if (age <= 70) bucket = '61-70';
        else if (age <= 80) bucket = '71-80';

        if (p.gender === 'Male') ageBuckets[bucket].Male++;
        else if (p.gender === 'Female') ageBuckets[bucket].Female++;
    });
    
    return Object.keys(ageBuckets).map(bucket => ({
        name: bucket,
        Male: ageBuckets[bucket].Male,
        Female: ageBuckets[bucket].Female,
    }));
};

export const processHourlyData = (data: OpdData[]) => {
    const hourlyCounts = Array(24).fill(0);
    data.forEach(row => {
        if (row.billdate) {
            try {
                const hour = new Date(String(row.billdate)).getHours();
                if (!isNaN(hour)) {
                    hourlyCounts[hour]++;
                }
            } catch(e) {
                // Ignore invalid date formats
            }
        }
    });

    return hourlyCounts.map((count, index) => ({
        name: `${index}:00`,
        Transactions: count
    }));
};

export const processServiceDonutData = (data: OpdData[], serviceType: string, groupBy: keyof OpdData) => {
    const filtered = data.filter(d => String(d.servicetype || '').toLowerCase().trim() === serviceType.toLowerCase());
    const counts = filtered.reduce((acc, curr) => {
        const value = curr[groupBy];
        const key = (value ? String(value).trim() : '') || 'Uncategorized';
        acc[key] = (acc[key] || 0) + 1;
        return acc;
    }, {} as { [key: string]: number });

    const sortedData = Object.entries(counts).sort((a, b) => b[1] - a[1]);
    const top5 = sortedData.slice(0, 5);
    const othersCount = sortedData.slice(5).reduce((sum, item) => sum + item[1], 0);

    let chartData = top5.map(([name, value]) => ({ name, value }));
    if (othersCount > 0) {
        chartData.push({ name: 'Others', value: othersCount });
    }
    return chartData;
};

export const processRevenueDonutData = (data: OpdData[]) => {
    const billedData = data.filter(d => String(d.trantype || '').toLowerCase().trim() === 'billed');
    const payerTypeRevenue = billedData.reduce((acc, row) => {
        const payer = String(row.payertype || '').trim() || 'Unknown';
        const amount = parseFloat(String(row.netamount || '0'));
        acc[payer] = (acc[payer] || 0) + amount;
        return acc;
    }, {} as {[key: string]: number});
    
    const sortedPayerData = Object.entries(payerTypeRevenue).sort((a, b) => b[1] - a[1]);
    const topPayers = sortedPayerData.slice(0, 4);
    const otherPayersAmount = sortedPayerData.slice(4).reduce((sum, item) => sum + item[1], 0);

    let chartData = topPayers.map(([name, value]) => ({ name, value }));
    if(otherPayersAmount > 0) {
        chartData.push({ name: 'Others', value: otherPayersAmount });
    }
    return chartData;
};
