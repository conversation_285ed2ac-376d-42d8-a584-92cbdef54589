
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { Kpi, OpdData } from "../types";

// IMPORTANT: Do not expose your API key in client-side code in a real application.
// This is for demonstration purposes only. In a production environment, this call
// should be made from a backend server where the API key can be kept secure.
const API_KEY = process.env.API_KEY;

if (!API_KEY) {
  // In a real app, you might want to disable the AI feature or show a message.
  console.warn("Gemini API key not found in environment variables. AI features will be disabled.");
}

const ai = new GoogleGenAI({ apiKey: API_KEY! });

function getTopItems(data: OpdData[], key: keyof OpdData, count: number = 3) {
    const itemCounts = data.reduce((acc, row) => {
        const value = row[key];
        const item = value ? String(value).trim() : undefined;
        if(item) {
            acc[item] = (acc[item] || 0) + 1;
        }
        return acc;
    }, {} as {[key: string]: number});

    return Object.entries(itemCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, count)
        .map(([name, value]) => `${name} (${value} visits)`)
        .join(', ');
}


export const generateDashboardInsights = async (kpis: Kpi, filteredData: OpdData[]): Promise<string> => {
    if (!API_KEY) {
        return "AI insights are unavailable. The API key is not configured.";
    }

    const topSpecialities = getTopItems(filteredData, 'performingdoctorspeciality');
    const busiestHours = getTopItems(filteredData.filter(d => d.billdate), 'billdate', 1).replace(/:\d+:\d+ .*/, ''); // Basic hour extraction

    const prompt = `
        You are a senior hospital operations analyst. Your task is to provide brief, actionable insights based on a daily Outpatient Department (OPD) data summary.
        Analyze the following data and generate 3-4 bullet points highlighting key observations, potential issues, and strategic recommendations.
        Focus on patient flow, revenue, and resource allocation. Use a professional but easy-to-understand tone.

        Today's OPD Data Summary:
        - Total Transactions: ${kpis.totalTransactions}
        - OPD Visits vs. Services: ${kpis.opdVisits} visits, ${kpis.opdServices} services
        - Total Unique Patients: ${kpis.totalUniquePatients}
        - New Patient Registrations Today: ${kpis.patientsRegisteredToday}
        - Total Revenue: INR ${kpis.totalRevenue.toFixed(2)}
        - Total Concessions: INR ${kpis.totalConcessions.toFixed(2)}
        - Busiest Specialities: ${topSpecialities || 'N/A'}
        - Note on Patient Types: ${kpis.newVisits} new visits, ${kpis.emergencyVisits} emergency cases.

        Based on this data, provide your analysis as a list of bullet points. Start each bullet with a relevant emoji.
    `;

    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: prompt,
            config: {
                temperature: 0.5,
                topK: 32,
                topP: 1,
            }
        });

        return response.text;
    } catch (error: any) {
        console.error("Error generating insights:", error);
        if (error.message.includes('API key not valid')) {
             return "Could not generate insights. The provided API Key is not valid. Please check your configuration.";
        }
        return "An error occurred while generating AI insights. Please check the console for details.";
    }
};
