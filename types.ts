
export interface OpdData {
    [key: string]: string | number | undefined;
    unit?: string;
    trantype?: string;
    servicetype?: string;
    uhid?: string;
    visittype?: string;
    registrationdate?: string;
    age?: string;
    gender?: string;
    performingdoctorspeciality?: string;
    performingdoctor?: string;
    billdate?: string;
    subspecialization?: string;
    netamount?: string;
    concessionamount?: string;
    payertype?: string;
}

export interface Kpi {
    totalTransactions: number;
    opdVisits: number;
    opdServices: number;
    totalUniquePatients: number;
    newVisits: number;
    emergencyVisits: number;
    otherVisits: number;
    patientsRegisteredToday: number;
    latestRegDate: string;
    totalRevenue: number;
    totalConcessions: number;
}
